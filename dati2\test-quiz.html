<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Test - QuizMaster</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container" style="padding: 2rem 0;">
        <h1>Quiz Engine Test</h1>
        <p>Click the buttons below to test different quiz categories:</p>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin: 2rem 0;">
            <button class="btn btn-primary" onclick="testQuiz(9, 'General Knowledge')">
                <i class="fas fa-brain"></i> General Knowledge
            </button>
            <button class="btn btn-primary" onclick="testQuiz(11, 'Movies')">
                <i class="fas fa-film"></i> Movies
            </button>
            <button class="btn btn-primary" onclick="testQuiz(17, 'Science')">
                <i class="fas fa-flask"></i> Science
            </button>
            <button class="btn btn-primary" onclick="testQuiz(21, 'Sports')">
                <i class="fas fa-gamepad"></i> Sports
            </button>
            <button class="btn btn-secondary" onclick="testRandomQuiz()">
                <i class="fas fa-random"></i> Random Quiz
            </button>
        </div>
        
        <div id="test-results" style="margin-top: 2rem; padding: 1rem; background: var(--bg-secondary); border-radius: var(--border-radius);">
            <h3>Test Results:</h3>
            <div id="test-output">Click a button above to test the quiz functionality.</div>
        </div>
    </div>

    <!-- Quiz Modal -->
    <div id="quiz-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="quiz-container">
                <!-- Quiz content will be loaded here -->
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/quiz.js"></script>
    <script src="assets/js/theme.js"></script>
    
    <script>
        // Test functions
        async function testQuiz(categoryId, categoryName) {
            const output = document.getElementById('test-output');
            output.innerHTML = `<i class="fas fa-spinner fa-spin"></i> Loading ${categoryName} quiz...`;
            
            try {
                const quizData = await fetchQuizData(categoryId, 5); // 5 questions for testing
                
                if (quizData.length > 0) {
                    output.innerHTML = `
                        <div style="color: var(--accent-color);">
                            <i class="fas fa-check-circle"></i> Successfully loaded ${quizData.length} questions for ${categoryName}
                        </div>
                        <div style="margin-top: 1rem; font-size: 0.9rem;">
                            <strong>Sample question:</strong><br>
                            ${decodeHtmlEntities(quizData[0].question)}
                        </div>
                    `;
                    
                    // Start the quiz
                    startQuiz(`test_${categoryId}`, quizData[0], quizData);
                } else {
                    output.innerHTML = `<div style="color: #ef4444;"><i class="fas fa-exclamation-triangle"></i> Failed to load quiz data for ${categoryName}</div>`;
                }
            } catch (error) {
                output.innerHTML = `<div style="color: #ef4444;"><i class="fas fa-exclamation-triangle"></i> Error: ${error.message}</div>`;
                console.error('Quiz test error:', error);
            }
        }
        
        async function testRandomQuiz() {
            const categories = [9, 11, 12, 17, 21, 23]; // Various categories
            const randomCategory = categories[Math.floor(Math.random() * categories.length)];
            const categoryNames = {
                9: 'General Knowledge',
                11: 'Movies',
                12: 'Music',
                17: 'Science',
                21: 'Sports',
                23: 'History'
            };
            
            await testQuiz(randomCategory, categoryNames[randomCategory]);
        }
        
        // Test API connectivity on page load
        document.addEventListener('DOMContentLoaded', async function() {
            const output = document.getElementById('test-output');
            output.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing API connectivity...';
            
            try {
                const response = await fetch('https://opentdb.com/api.php?amount=1&category=9');
                const data = await response.json();
                
                if (data.response_code === 0) {
                    output.innerHTML = `
                        <div style="color: var(--accent-color);">
                            <i class="fas fa-check-circle"></i> API connectivity test successful!
                        </div>
                        <div style="margin-top: 0.5rem; font-size: 0.9rem;">
                            OpenTDB API is responding correctly. You can now test the quiz functionality.
                        </div>
                    `;
                } else {
                    output.innerHTML = `
                        <div style="color: #ef4444;">
                            <i class="fas fa-exclamation-triangle"></i> API returned error code: ${data.response_code}
                        </div>
                    `;
                }
            } catch (error) {
                output.innerHTML = `
                    <div style="color: #ef4444;">
                        <i class="fas fa-exclamation-triangle"></i> API connectivity test failed: ${error.message}
                    </div>
                    <div style="margin-top: 0.5rem; font-size: 0.9rem;">
                        This might be due to CORS restrictions or network issues.
                    </div>
                `;
                console.error('API test error:', error);
            }
        });
    </script>
</body>
</html>
