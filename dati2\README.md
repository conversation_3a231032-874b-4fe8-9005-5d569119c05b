# QuizMaster - Interactive Quiz Platform

A comprehensive, fully responsive quiz website built with modern web technologies. QuizMaster provides an engaging platform for educational quizzes, trivia, and knowledge testing across multiple categories.

## 🌟 Features

### Core Functionality
- **Interactive Quiz Engine**: Dynamic quiz system with multiple-choice questions
- **Multiple Categories**: General Knowledge, Science, History, Movies, Sports, and more
- **Real-time Scoring**: Instant feedback and detailed results
- **Progress Tracking**: Visual progress indicators during quizzes
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices

### Content Management
- **Blog System**: 10+ educational articles (800-1000 words each)
- **Dynamic Content**: API-driven quiz questions and AI-generated images
- **SEO Optimized**: Proper meta tags, structured data, and semantic HTML

### User Experience
- **Dark/Light Theme**: Toggle between themes with persistent preferences
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support
- **Performance**: Optimized loading times and smooth animations
- **Mobile-First**: Responsive design with collapsible navigation

## 🛠️ Technology Stack

### Frontend
- **HTML5**: Semantic markup with proper accessibility
- **CSS3**: Modern styling with CSS Grid, Flexbox, and custom properties
- **JavaScript (ES6+)**: Vanilla JavaScript with modern features
- **Font Awesome**: Icon library for consistent iconography
- **Google Fonts**: Inter font family for optimal readability

### APIs & Services
- **OpenTDB API**: Trivia questions database
- **Pollinations AI**: Dynamic image generation
- **No Backend Required**: Fully client-side application

### Development Tools
- **Responsive Design**: Mobile-first approach with breakpoints
- **CSS Variables**: Theme system with dark/light mode support
- **Modular JavaScript**: Organized code structure with separate modules

## 📁 Project Structure

```
dati2/
├── index.html                 # Homepage
├── about.html                 # About Us page
├── contact.html               # Contact page
├── privacy.html               # Privacy Policy
├── terms.html                 # Terms of Service
├── test-quiz.html             # Quiz testing page
├── api-test.html              # API integration testing
├── README.md                  # Project documentation
├── assets/
│   ├── css/
│   │   ├── style.css          # Main stylesheet
│   │   └── responsive.css     # Responsive design styles
│   └── js/
│       ├── main.js            # Core functionality
│       ├── quiz.js            # Quiz engine
│       └── theme.js           # Theme management
└── blog/
    ├── index.html             # Blog homepage
    ├── the-science-of-learning.html
    ├── memory-techniques-for-quiz-masters.html
    ├── fascinating-history-facts.html
    ├── science-quiz-preparation-guide.html
    ├── geography-mastery-tips.html
    └── movie-trivia-secrets.html
```

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection (for API calls)
- Local web server (optional, for development)

### Installation
1. Clone or download the project files
2. Open `index.html` in a web browser
3. For development, use a local server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

### Testing
- Open `test-quiz.html` to test quiz functionality
- Open `api-test.html` to verify API integrations
- Use browser developer tools to test responsive design

## 🎯 Usage

### Taking Quizzes
1. Visit the homepage
2. Choose a category or start a random quiz
3. Answer questions by clicking on options
4. View your results and detailed explanations
5. Share your scores or try again

### Browsing Content
- **Blog**: Educational articles about quiz strategies and trivia
- **Categories**: Browse quizzes by subject area
- **About**: Learn about the platform and team
- **Contact**: Get in touch for support or feedback

### Customization
- **Theme**: Toggle between light and dark modes
- **Difficulty**: Choose from easy, medium, or hard questions
- **Categories**: Filter quizzes by subject area

## 🔧 Configuration

### API Settings
The application uses external APIs that require no authentication:

```javascript
// OpenTDB API for quiz questions
const OPENTDB_BASE_URL = 'https://opentdb.com/api.php';

// Pollinations AI for image generation
const POLLINATIONS_BASE_URL = 'https://image.pollinations.ai/prompt/';
```

### Theme Configuration
Themes are managed through CSS custom properties:

```css
:root {
    --primary-color: #6366f1;
    --bg-primary: #ffffff;
    --text-primary: #1f2937;
    /* ... more variables */
}

[data-theme="dark"] {
    --bg-primary: #111827;
    --text-primary: #f9fafb;
    /* ... dark theme overrides */
}
```

## 📱 Responsive Breakpoints

- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+
- **Large Desktop**: 1200px+

## 🎨 Design System

### Colors
- **Primary**: #6366f1 (Indigo)
- **Secondary**: #f59e0b (Amber)
- **Accent**: #10b981 (Emerald)
- **Error**: #ef4444 (Red)

### Typography
- **Font Family**: Inter (Google Fonts)
- **Headings**: 600-700 weight
- **Body**: 400-500 weight
- **Line Height**: 1.6 for readability

### Components
- **Buttons**: Consistent styling with hover effects
- **Cards**: Elevated design with shadows
- **Forms**: Accessible inputs with focus states
- **Navigation**: Responsive with mobile hamburger menu

## 🔍 SEO Features

- **Meta Tags**: Proper title, description, and keywords
- **Open Graph**: Social media sharing optimization
- **Structured Data**: Schema.org markup for search engines
- **Semantic HTML**: Proper heading hierarchy and landmarks
- **Alt Text**: Descriptive alternative text for images

## ♿ Accessibility

- **WCAG 2.1 AA Compliance**: Meets accessibility standards
- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: ARIA labels and semantic markup
- **Color Contrast**: Sufficient contrast ratios
- **Focus Management**: Visible focus indicators

## 🚀 Performance

- **Optimized Images**: Responsive images with lazy loading
- **Minified Assets**: Compressed CSS and JavaScript
- **Caching**: Browser caching for static assets
- **API Efficiency**: Optimized API calls with error handling

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **OpenTDB**: Free trivia questions database
- **Pollinations AI**: AI-powered image generation
- **Font Awesome**: Icon library
- **Google Fonts**: Web font service
- **Anthropic**: Claude AI assistance in development

## 📞 Support

For support, feature requests, or bug reports:
- Email: <EMAIL>
- Website: Contact form available on the site
- Issues: Use the project's issue tracker

---

**QuizMaster** - Making learning fun through interactive quizzes! 🧠✨
