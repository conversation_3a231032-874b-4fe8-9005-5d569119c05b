// Main JavaScript functionality

// DOM Elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');
const modal = document.getElementById('quiz-modal');
const closeModal = document.querySelector('.close');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadRecentQuizzes();
    setupStatsAnimation();
});

// Initialize application
function initializeApp() {
    setupMobileMenu();
    setupModal();
    setupSmoothScrolling();
    setupLazyLoading();
}

// Mobile menu functionality
function setupMobileMenu() {
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function() {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    }
}

// Modal functionality
function setupModal() {
    if (closeModal) {
        closeModal.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    }

    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display === 'block') {
            modal.style.display = 'none';
        }
    });
}

// Smooth scrolling for anchor links
function setupSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Lazy loading for images
function setupLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }
}

// Load recent quizzes from OpenTDB API
async function loadRecentQuizzes() {
    const quizGrid = document.getElementById('recent-quiz-grid');
    if (!quizGrid) return;

    try {
        showLoadingState(quizGrid);
        
        // Fetch different categories for variety
        const categories = [9, 11, 12, 17, 21, 23]; // General, Movies, Music, Science, Sports, History
        const quizPromises = categories.map(category => 
            fetchQuizData(category, 2) // 2 questions per category
        );

        const quizResults = await Promise.all(quizPromises);
        const allQuizzes = quizResults.flat();
        
        displayQuizzes(allQuizzes, quizGrid);
    } catch (error) {
        console.error('Error loading quizzes:', error);
        showErrorState(quizGrid);
    }
}

// Fetch quiz data from OpenTDB API
async function fetchQuizData(category, amount = 10) {
    const url = `https://opentdb.com/api.php?amount=${amount}&category=${category}&type=multiple`;
    
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.response_code === 0) {
            return data.results.map(question => ({
                ...question,
                id: generateQuizId(),
                image: generateQuizImage(question.category)
            }));
        } else {
            throw new Error('API returned error code: ' + data.response_code);
        }
    } catch (error) {
        console.error('Error fetching quiz data:', error);
        return [];
    }
}

// Generate quiz image using Pollinations AI
function generateQuizImage(category) {
    const prompts = {
        'General Knowledge': 'colorful brain with question marks and lightbulbs',
        'Sports': 'sports equipment and athletes in action',
        'History': 'ancient monuments and historical artifacts',
        'Science: Nature': 'beautiful nature scenes with scientific elements',
        'Entertainment: Film': 'movie theater and film reels',
        'Entertainment: Music': 'musical instruments and notes',
        'Science & Nature': 'laboratory equipment and nature elements'
    };
    
    const prompt = prompts[category] || 'quiz and trivia illustration';
    return `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}?width=400&height=250`;
}

// Generate unique quiz ID
function generateQuizId() {
    return 'quiz_' + Math.random().toString(36).substr(2, 9);
}

// Display quizzes in the grid
function displayQuizzes(quizzes, container) {
    container.innerHTML = '';
    
    quizzes.forEach(quiz => {
        const quizCard = createQuizCard(quiz);
        container.appendChild(quizCard);
    });
}

// Create quiz card element
function createQuizCard(quiz) {
    const card = document.createElement('div');
    card.className = 'quiz-card';
    card.innerHTML = `
        <img src="${quiz.image}" alt="${quiz.category}" loading="lazy" onerror="this.src='https://via.placeholder.com/400x250?text=Quiz'">
        <div class="quiz-card-content">
            <h3>${truncateText(quiz.question, 60)}</h3>
            <p>${quiz.category}</p>
            <div class="quiz-meta">
                <span><i class="fas fa-clock"></i> ~2 min</span>
                <span><i class="fas fa-star"></i> ${quiz.difficulty}</span>
            </div>
            <button class="btn btn-primary" onclick="startQuiz('${quiz.id}', ${JSON.stringify(quiz).replace(/"/g, '&quot;')})">
                <i class="fas fa-play"></i> Start Quiz
            </button>
        </div>
    `;
    return card;
}

// Utility function to truncate text
function truncateText(text, maxLength) {
    // Decode HTML entities
    const decoded = decodeHtmlEntities(text);
    return decoded.length > maxLength ? decoded.substring(0, maxLength) + '...' : decoded;
}

// Decode HTML entities
function decodeHtmlEntities(text) {
    const textArea = document.createElement('textarea');
    textArea.innerHTML = text;
    return textArea.value;
}

// Show loading state
function showLoadingState(container) {
    container.innerHTML = `
        <div class="loading-state" style="grid-column: 1 / -1; text-align: center; padding: 2rem;">
            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--primary-color);"></i>
            <p style="margin-top: 1rem; color: var(--text-secondary);">Loading amazing quizzes...</p>
        </div>
    `;
}

// Show error state
function showErrorState(container) {
    container.innerHTML = `
        <div class="error-state" style="grid-column: 1 / -1; text-align: center; padding: 2rem;">
            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #ef4444;"></i>
            <p style="margin-top: 1rem; color: var(--text-secondary);">Sorry, we couldn't load the quizzes right now.</p>
            <button class="btn btn-primary" onclick="loadRecentQuizzes()" style="margin-top: 1rem;">
                <i class="fas fa-redo"></i> Try Again
            </button>
        </div>
    `;
}

// Start a random quiz
async function startRandomQuiz() {
    try {
        const randomCategory = Math.floor(Math.random() * 24) + 9; // Random category 9-32
        const quizData = await fetchQuizData(randomCategory, 1);
        
        if (quizData.length > 0) {
            startQuiz(quizData[0].id, quizData[0]);
        } else {
            alert('Sorry, could not load a random quiz. Please try again.');
        }
    } catch (error) {
        console.error('Error starting random quiz:', error);
        alert('Sorry, could not load a random quiz. Please try again.');
    }
}

// Load quiz by category
async function loadQuizCategory(categoryId) {
    try {
        const quizData = await fetchQuizData(categoryId, 10);
        
        if (quizData.length > 0) {
            // Start with first question of the category
            startQuiz(`category_${categoryId}`, quizData[0], quizData);
        } else {
            alert('Sorry, could not load quizzes for this category. Please try again.');
        }
    } catch (error) {
        console.error('Error loading category quiz:', error);
        alert('Sorry, could not load quizzes for this category. Please try again.');
    }
}

// Scroll to categories section
function scrollToCategories() {
    const categoriesSection = document.getElementById('categories');
    if (categoriesSection) {
        categoriesSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Load more quizzes
async function loadMoreQuizzes() {
    const quizGrid = document.getElementById('recent-quiz-grid');
    if (!quizGrid) return;

    try {
        // Show loading indicator
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'loading-more';
        loadingDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading more quizzes...';
        loadingDiv.style.gridColumn = '1 / -1';
        loadingDiv.style.textAlign = 'center';
        loadingDiv.style.padding = '1rem';
        quizGrid.appendChild(loadingDiv);

        // Fetch more quizzes
        const categories = [10, 14, 15, 16, 18, 19, 20, 22, 24, 25]; // Additional categories
        const randomCategories = categories.sort(() => 0.5 - Math.random()).slice(0, 3);
        
        const quizPromises = randomCategories.map(category => 
            fetchQuizData(category, 2)
        );

        const quizResults = await Promise.all(quizPromises);
        const newQuizzes = quizResults.flat();
        
        // Remove loading indicator
        quizGrid.removeChild(loadingDiv);
        
        // Add new quizzes
        newQuizzes.forEach(quiz => {
            const quizCard = createQuizCard(quiz);
            quizGrid.appendChild(quizCard);
        });
        
    } catch (error) {
        console.error('Error loading more quizzes:', error);
        // Remove loading indicator if it exists
        const loadingDiv = quizGrid.querySelector('.loading-more');
        if (loadingDiv) {
            quizGrid.removeChild(loadingDiv);
        }
    }
}

// Utility function to format difficulty
function formatDifficulty(difficulty) {
    return difficulty.charAt(0).toUpperCase() + difficulty.slice(1);
}

// Utility function to get category icon
function getCategoryIcon(category) {
    const icons = {
        'General Knowledge': 'fas fa-brain',
        'Sports': 'fas fa-football-ball',
        'History': 'fas fa-landmark',
        'Science: Nature': 'fas fa-leaf',
        'Entertainment: Film': 'fas fa-film',
        'Entertainment: Music': 'fas fa-music'
    };

    return icons[category] || 'fas fa-question-circle';
}

// Setup stats animation
function setupStatsAnimation() {
    const statNumbers = document.querySelectorAll('.stat-number');

    if ('IntersectionObserver' in window) {
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateStatNumber(entry.target);
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        statNumbers.forEach(stat => statsObserver.observe(stat));
    } else {
        // Fallback for browsers without IntersectionObserver
        statNumbers.forEach(stat => animateStatNumber(stat));
    }
}

// Animate stat number counting up
function animateStatNumber(element) {
    const target = parseInt(element.getAttribute('data-target'));
    const duration = 2000; // 2 seconds
    const increment = target / (duration / 16); // 60fps
    let current = 0;

    const timer = setInterval(() => {
        current += increment;

        if (current >= target) {
            current = target;
            clearInterval(timer);
        }

        element.textContent = formatStatNumber(Math.floor(current));
    }, 16);
}

// Format stat numbers with commas
function formatStatNumber(num) {
    if (num >= 1000) {
        return (num / 1000).toFixed(num >= 10000 ? 0 : 1) + 'k';
    }
    return num.toLocaleString();
}

// Add scroll-triggered animations for other elements
function setupScrollAnimations() {
    const animatedElements = document.querySelectorAll('.category-card, .quiz-card, .featured-quiz');

    if ('IntersectionObserver' in window) {
        const animationObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });

        animatedElements.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            animationObserver.observe(element);
        });
    }
}

// Call scroll animations setup
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(setupScrollAnimations, 100);
});
