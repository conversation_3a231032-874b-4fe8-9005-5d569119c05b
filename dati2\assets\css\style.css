/* CSS Variables for Theme Support */
:root {
    --primary-color: #6366f1;
    --primary-hover: #5855eb;
    --secondary-color: #f59e0b;
    --accent-color: #10b981;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --border-color: #374151;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: var(--transition);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.5rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-hover);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background-color: var(--bg-primary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    color: var(--text-primary);
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
}

/* Dropdown */
.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--bg-primary);
    min-width: 200px;
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius);
    padding: 0.5rem 0;
    z-index: 1000;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown-content a {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    transition: var(--transition);
}

.dropdown-content a:hover {
    background-color: var(--bg-secondary);
    color: var(--primary-color);
}

/* Theme Toggle */
.theme-toggle {
    margin-left: 1rem;
}

.theme-checkbox {
    display: none;
}

.theme-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 50px;
    height: 25px;
    background-color: var(--bg-tertiary);
    border-radius: 25px;
    padding: 3px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.theme-label i {
    font-size: 0.8rem;
    z-index: 1;
}

.theme-label::after {
    content: '';
    position: absolute;
    width: 19px;
    height: 19px;
    background-color: var(--bg-primary);
    border-radius: 50%;
    transition: var(--transition);
    left: 3px;
}

.theme-checkbox:checked + .theme-label::after {
    transform: translateX(25px);
}

/* Mobile Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    transition: var(--transition);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    color: white;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #d97706;
    color: white;
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Hero Section */
.hero {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

/* Sections */
.section-title {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--text-primary);
}

.categories {
    padding: 4rem 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.category-card {
    background-color: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.category-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.category-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.category-card p {
    margin-bottom: 1.5rem;
}

/* Stats Section */
.stats-section {
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item {
    padding: 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Featured Quizzes */
.featured-quizzes {
    padding: 4rem 0;
    background-color: var(--bg-secondary);
}

.featured-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 2rem;
}

.featured-quiz {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.featured-quiz:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.featured-quiz.large {
    grid-row: span 2;
    flex-direction: row;
    align-items: center;
}

.featured-content {
    padding: 2rem;
    flex: 1;
}

.featured-quiz.large .featured-content {
    flex: 1;
}

.featured-image {
    flex: 1;
    min-height: 200px;
}

.featured-quiz.large .featured-image {
    min-height: auto;
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--secondary-color);
    color: white;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.featured-quiz h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.featured-quiz p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.featured-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: var(--text-muted);
    flex-wrap: wrap;
}

.featured-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Recent Quizzes */
.recent-quizzes {
    padding: 4rem 0;
    background-color: var(--bg-primary);
}

.quiz-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.quiz-card {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.quiz-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.quiz-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.quiz-card-content {
    padding: 1.5rem;
}

.quiz-card h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.quiz-card p {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.quiz-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.text-center {
    text-align: center;
}

/* Footer */
.footer {
    background-color: var(--bg-secondary);
    padding: 3rem 0 1rem;
    border-top: 1px solid var(--border-color);
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--text-secondary);
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--bg-primary);
    margin: 5% auto;
    padding: 0;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    color: var(--text-muted);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 1;
    transition: var(--transition);
}

.close:hover {
    color: var(--text-primary);
}

/* Quiz Styles */
.quiz-container {
    padding: 2rem;
}

.quiz-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.quiz-category {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.quiz-category i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.quiz-category h2 {
    margin: 0;
    color: var(--text-primary);
}

.quiz-progress {
    width: 100%;
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: 4px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.quiz-progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.question-container {
    margin-bottom: 2rem;
}

.question-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    line-height: 1.4;
}

.answers-container {
    display: grid;
    gap: 1rem;
}

.answer-option {
    padding: 1rem;
    background-color: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-align: left;
}

.answer-option:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-tertiary);
}

.answer-option.selected {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: white;
}

.answer-option.correct {
    border-color: var(--accent-color);
    background-color: var(--accent-color);
    color: white;
}

.answer-option.incorrect {
    border-color: #ef4444;
    background-color: #ef4444;
    color: white;
}

.quiz-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
}

.quiz-results {
    text-align: center;
    padding: 2rem;
}

.score-display {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.score-message {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Utility Classes */
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

/* Blog Styles */
.blog-hero {
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    text-align: center;
}

.blog-hero-content h1 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.blog-hero-content p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.featured-article {
    padding: 3rem 0;
}

.featured-post {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.featured-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.featured-content {
    padding: 2rem;
}

.featured-tag {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.featured-content h2 {
    margin-bottom: 1rem;
}

.featured-content h2 a {
    color: var(--text-primary);
    text-decoration: none;
}

.featured-content h2 a:hover {
    color: var(--primary-color);
}

.post-meta {
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
    font-size: 0.9rem;
    color: var(--text-muted);
    flex-wrap: wrap;
}

.post-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.blog-posts {
    padding: 3rem 0;
    background-color: var(--bg-secondary);
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.blog-card {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.blog-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.blog-content {
    padding: 1.5rem;
}

.blog-category {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.blog-content h3 {
    margin-bottom: 1rem;
}

.blog-content h3 a {
    color: var(--text-primary);
    text-decoration: none;
}

.blog-content h3 a:hover {
    color: var(--primary-color);
}

.blog-content p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.blog-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: var(--text-muted);
    flex-wrap: wrap;
}

.blog-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.newsletter-signup {
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    text-align: center;
}

.newsletter-content h2 {
    color: white;
    margin-bottom: 1rem;
}

.newsletter-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
    flex-wrap: wrap;
}

.newsletter-form input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    min-width: 250px;
}

.newsletter-form button {
    white-space: nowrap;
}

/* Article Styles */
.article-header {
    padding: 2rem 0;
    background-color: var(--bg-secondary);
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.back-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.back-link:hover {
    color: var(--primary-hover);
}

.article-category {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.article-title {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.article-info {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    color: var(--text-muted);
    flex-wrap: wrap;
}

.article-info span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.article-image {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.article-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.article-content {
    padding: 3rem 0;
}

.article-body {
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.article-body .lead {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-secondary);
    border-left: 4px solid var(--primary-color);
    border-radius: var(--border-radius);
}

.article-body h2 {
    color: var(--text-primary);
    margin: 2rem 0 1rem;
    font-size: 1.8rem;
}

.article-body h3 {
    color: var(--text-primary);
    margin: 1.5rem 0 1rem;
    font-size: 1.4rem;
}

.article-body p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.article-body ul,
.article-body ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.article-body li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.article-body strong {
    color: var(--text-primary);
    font-weight: 600;
}

.article-navigation {
    max-width: 800px;
    margin: 3rem auto 0;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.article-navigation .nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.article-navigation .nav-link:hover {
    background-color: var(--bg-tertiary);
    transform: translateX(5px);
}

.article-navigation .nav-link span {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.article-navigation .nav-link strong {
    display: block;
    margin-top: 0.25rem;
    font-size: 1.1rem;
}

.share-buttons {
    max-width: 800px;
    margin: 2rem auto 0;
    text-align: center;
}

.share-buttons h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.share-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    color: white;
}

.share-btn.facebook {
    background-color: #1877f2;
}

.share-btn.twitter {
    background-color: #1da1f2;
}

.share-btn.linkedin {
    background-color: #0077b5;
}

.share-btn.copy {
    background-color: var(--text-muted);
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
}

/* About Page Styles */
.about-hero {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    text-align: center;
}

.about-hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: white;
}

.about-hero-content p {
    font-size: 1.3rem;
    opacity: 0.9;
}

.mission-section {
    padding: 4rem 0;
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.mission-text h2 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 2.2rem;
}

.mission-text p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.7;
}

.mission-values {
    display: grid;
    gap: 1.5rem;
    margin-top: 2rem;
}

.value-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.value-item i {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.value-item h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.value-item p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
}

.mission-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

.team-section {
    padding: 4rem 0;
    background-color: var(--bg-secondary);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.team-member {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.member-image {
    margin-bottom: 1.5rem;
}

.member-image img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--primary-color);
}

.member-info h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.member-role {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.member-info p:last-child {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

.about-stats {
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
}

.technology-section {
    padding: 4rem 0;
}

.tech-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.tech-text h2 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 2.2rem;
}

.tech-text p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.tech-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.feature-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.feature-item span {
    color: var(--text-primary);
    font-weight: 500;
}

.tech-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

.contact-cta {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    color: white;
    margin-bottom: 1rem;
    font-size: 2.5rem;
}

.cta-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    font-size: 1.2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-buttons .btn-outline {
    background-color: transparent;
    color: white;
    border-color: white;
}

.cta-buttons .btn-outline:hover {
    background-color: white;
    color: var(--primary-color);
}

.company-info {
    margin-top: 1rem;
    font-size: 0.9rem;
}

.company-info p {
    margin-bottom: 0.25rem;
    color: var(--text-muted);
}

/* Contact Page Styles */
.contact-hero {
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    text-align: center;
}

.contact-hero-content h1 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.contact-hero-content p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.contact-form-section {
    padding: 4rem 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h2,
.contact-form h2 {
    color: var(--text-primary);
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

.contact-methods {
    margin-bottom: 2rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.contact-method i {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.contact-method h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.contact-method p {
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.social-contact {
    margin-top: 2rem;
}

.social-contact h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.faq-section {
    padding: 4rem 0;
    background-color: var(--bg-secondary);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.faq-item {
    background-color: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.faq-item h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.faq-item p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

.response-section {
    padding: 3rem 0;
}

.response-content h2 {
    text-align: center;
    color: var(--text-primary);
    margin-bottom: 3rem;
    font-size: 2rem;
}

.response-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.response-item {
    text-align: center;
    padding: 2rem;
}

.response-item i {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.response-item h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.response-item p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Legal Pages Styles */
.legal-hero {
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    text-align: center;
}

.legal-hero-content h1 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.legal-hero-content p {
    color: var(--text-muted);
    font-size: 1rem;
}

.legal-content {
    padding: 4rem 0;
}

.legal-document {
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.7;
}

.legal-document h2 {
    color: var(--text-primary);
    margin: 2.5rem 0 1rem;
    font-size: 1.8rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.legal-document h3 {
    color: var(--text-primary);
    margin: 2rem 0 1rem;
    font-size: 1.3rem;
}

.legal-document p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.legal-document ul {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.legal-document li {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.legal-document strong {
    color: var(--text-primary);
}

.legal-footer {
    margin-top: 3rem;
    padding: 2rem;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.legal-footer p {
    margin: 0;
    font-weight: 500;
    color: var(--text-primary);
}

/* API Test Page Styles */
.test-hero {
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    text-align: center;
}

.test-hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: white;
}

.test-hero-content p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.api-test-section {
    padding: 3rem 0;
    border-bottom: 1px solid var(--border-color);
}

.api-test-section:last-of-type {
    border-bottom: none;
}

.test-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    align-items: end;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.control-group input,
.control-group select {
    padding: 0.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    min-width: 120px;
}

.control-group input:focus,
.control-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.test-results {
    background-color: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    min-height: 100px;
}

.success-result {
    color: var(--accent-color);
}

.success-result h3 {
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.success-result p {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.error-result {
    color: #ef4444;
}

.error-result h3 {
    color: #ef4444;
    margin-bottom: 1rem;
}

.error-result p {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.test-details {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.test-details h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.test-details p {
    font-family: monospace;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.generated-image {
    text-align: center;
    margin-top: 1rem;
}

.status-section {
    padding: 3rem 0;
    background-color: var(--bg-secondary);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.status-item {
    background-color: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.status-item i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-muted);
}

.status-item h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.status-text {
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
}

.status-item.success {
    border-color: var(--accent-color);
    background-color: rgba(16, 185, 129, 0.05);
}

.status-item.success i {
    color: var(--accent-color);
}

.status-item.success .status-text {
    color: var(--accent-color);
}

.status-item.error {
    border-color: #ef4444;
    background-color: rgba(239, 68, 68, 0.05);
}

.status-item.error i {
    color: #ef4444;
}

.status-item.error .status-text {
    color: #ef4444;
}

.hidden { display: none !important; }
.visible { display: block !important; }
