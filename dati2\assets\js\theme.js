// Theme management functionality

// Theme configuration
const THEME_KEY = 'quiz-master-theme';
const THEMES = {
    LIGHT: 'light',
    DARK: 'dark'
};

// Initialize theme system
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    setupThemeToggle();
});

// Initialize theme based on user preference or system preference
function initializeTheme() {
    const savedTheme = getSavedTheme();
    const systemTheme = getSystemTheme();
    const initialTheme = savedTheme || systemTheme;
    
    setTheme(initialTheme);
    updateThemeToggle(initialTheme);
}

// Get saved theme from localStorage
function getSavedTheme() {
    return localStorage.getItem(THEME_KEY);
}

// Get system theme preference
function getSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return THEMES.DARK;
    }
    return THEMES.LIGHT;
}

// Set theme
function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${theme}`);
    
    // Save theme preference
    localStorage.setItem(THEME_KEY, theme);
    
    // Update meta theme-color for mobile browsers
    updateMetaThemeColor(theme);
    
    // Dispatch theme change event
    window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
}

// Update meta theme-color for mobile browsers
function updateMetaThemeColor(theme) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    
    if (!metaThemeColor) {
        metaThemeColor = document.createElement('meta');
        metaThemeColor.name = 'theme-color';
        document.head.appendChild(metaThemeColor);
    }
    
    const colors = {
        [THEMES.LIGHT]: '#6366f1',
        [THEMES.DARK]: '#111827'
    };
    
    metaThemeColor.content = colors[theme];
}

// Setup theme toggle functionality
function setupThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    
    if (themeToggle) {
        themeToggle.addEventListener('change', function() {
            const currentTheme = getCurrentTheme();
            const newTheme = currentTheme === THEMES.LIGHT ? THEMES.DARK : THEMES.LIGHT;
            
            setTheme(newTheme);
            updateThemeToggle(newTheme);
            
            // Add smooth transition effect
            addThemeTransition();
        });
    }
}

// Get current theme
function getCurrentTheme() {
    return document.documentElement.getAttribute('data-theme') || THEMES.LIGHT;
}

// Update theme toggle state
function updateThemeToggle(theme) {
    const themeToggle = document.getElementById('theme-toggle');
    
    if (themeToggle) {
        themeToggle.checked = theme === THEMES.DARK;
        
        // Update aria-label for accessibility
        const label = theme === THEMES.DARK ? 'Switch to light mode' : 'Switch to dark mode';
        themeToggle.setAttribute('aria-label', label);
    }
}

// Add smooth transition effect when switching themes
function addThemeTransition() {
    const transitionClass = 'theme-transition';
    
    // Add transition class
    document.body.classList.add(transitionClass);
    
    // Remove transition class after animation
    setTimeout(() => {
        document.body.classList.remove(transitionClass);
    }, 300);
}

// Listen for system theme changes
if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    mediaQuery.addEventListener('change', function(e) {
        // Only update if user hasn't manually set a theme
        if (!getSavedTheme()) {
            const newTheme = e.matches ? THEMES.DARK : THEMES.LIGHT;
            setTheme(newTheme);
            updateThemeToggle(newTheme);
        }
    });
}

// Theme utility functions
const ThemeUtils = {
    // Get current theme
    getCurrentTheme: getCurrentTheme,
    
    // Set theme programmatically
    setTheme: setTheme,
    
    // Toggle theme
    toggleTheme: function() {
        const currentTheme = getCurrentTheme();
        const newTheme = currentTheme === THEMES.LIGHT ? THEMES.DARK : THEMES.LIGHT;
        setTheme(newTheme);
        updateThemeToggle(newTheme);
        return newTheme;
    },
    
    // Check if dark theme is active
    isDarkTheme: function() {
        return getCurrentTheme() === THEMES.DARK;
    },
    
    // Check if light theme is active
    isLightTheme: function() {
        return getCurrentTheme() === THEMES.LIGHT;
    },
    
    // Reset to system theme
    resetToSystemTheme: function() {
        localStorage.removeItem(THEME_KEY);
        const systemTheme = getSystemTheme();
        setTheme(systemTheme);
        updateThemeToggle(systemTheme);
        return systemTheme;
    }
};

// Make ThemeUtils available globally
window.ThemeUtils = ThemeUtils;

// Add CSS for theme transition
const themeTransitionCSS = `
    .theme-transition * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
    }
`;

// Inject transition CSS
const styleElement = document.createElement('style');
styleElement.textContent = themeTransitionCSS;
document.head.appendChild(styleElement);

// Theme-aware image loading
function updateThemeAwareImages() {
    const themeImages = document.querySelectorAll('[data-light-src][data-dark-src]');
    const currentTheme = getCurrentTheme();
    
    themeImages.forEach(img => {
        const lightSrc = img.getAttribute('data-light-src');
        const darkSrc = img.getAttribute('data-dark-src');
        
        if (currentTheme === THEMES.DARK && darkSrc) {
            img.src = darkSrc;
        } else if (currentTheme === THEMES.LIGHT && lightSrc) {
            img.src = lightSrc;
        }
    });
}

// Listen for theme changes to update images
window.addEventListener('themeChanged', updateThemeAwareImages);

// Accessibility improvements
function setupThemeAccessibility() {
    // Add keyboard support for theme toggle
    const themeToggle = document.getElementById('theme-toggle');
    
    if (themeToggle) {
        // Add focus styles
        themeToggle.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        themeToggle.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
        
        // Add keyboard support (Space and Enter)
        themeToggle.addEventListener('keydown', function(e) {
            if (e.key === ' ' || e.key === 'Enter') {
                e.preventDefault();
                this.click();
            }
        });
    }
}

// Initialize accessibility features
document.addEventListener('DOMContentLoaded', setupThemeAccessibility);

// High contrast mode detection and handling
if (window.matchMedia) {
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    
    function handleHighContrast(e) {
        if (e.matches) {
            document.body.classList.add('high-contrast');
        } else {
            document.body.classList.remove('high-contrast');
        }
    }
    
    // Initial check
    handleHighContrast(highContrastQuery);
    
    // Listen for changes
    highContrastQuery.addEventListener('change', handleHighContrast);
}

// Reduced motion detection
if (window.matchMedia) {
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    function handleReducedMotion(e) {
        if (e.matches) {
            document.body.classList.add('reduced-motion');
        } else {
            document.body.classList.remove('reduced-motion');
        }
    }
    
    // Initial check
    handleReducedMotion(reducedMotionQuery);
    
    // Listen for changes
    reducedMotionQuery.addEventListener('change', handleReducedMotion);
}

// Export theme constants for use in other modules
window.THEMES = THEMES;

// Debug function for development
window.debugTheme = function() {
    console.log('Current theme:', getCurrentTheme());
    console.log('Saved theme:', getSavedTheme());
    console.log('System theme:', getSystemTheme());
    console.log('Theme toggle state:', document.getElementById('theme-toggle')?.checked);
};
