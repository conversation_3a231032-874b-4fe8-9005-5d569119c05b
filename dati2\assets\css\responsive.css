/* Responsive Design */

/* Large screens (1200px and up) */
@media (min-width: 1200px) {
    .hero-title {
        font-size: 3.5rem;
    }
    
    .categories-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .quiz-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Medium screens (768px to 1199px) */
@media (max-width: 1199px) {
    .hero-container {
        gap: 2rem;
    }
    
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quiz-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Tablet screens (768px to 991px) */
@media (max-width: 991px) {
    .nav-menu {
        gap: 1.5rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .quiz-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .featured-grid {
        grid-template-columns: 1fr 1fr;
    }

    .featured-quiz.large {
        grid-column: 1 / -1;
        flex-direction: column;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile screens (768px and below) */
@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-primary);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-lg);
        padding: 2rem 0;
        gap: 1rem;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 0.5rem 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }

    /* Dropdown for mobile */
    .dropdown-content {
        position: static;
        display: block;
        box-shadow: none;
        background-color: var(--bg-secondary);
        margin-top: 0.5rem;
        border-radius: var(--border-radius);
    }

    .dropdown:hover .dropdown-content {
        display: block;
    }

    /* Hero Section */
    .hero {
        padding: 2rem 0;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    /* Categories */
    .categories {
        padding: 2rem 0;
    }

    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .category-card {
        padding: 1.5rem;
    }

    /* Stats Section */
    .stats-section {
        padding: 2rem 0;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    /* Featured Quizzes */
    .featured-quizzes {
        padding: 2rem 0;
    }

    .featured-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .featured-quiz.large {
        flex-direction: column;
    }

    .featured-content {
        padding: 1.5rem;
    }

    .featured-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    /* Blog Responsive */
    .blog-hero {
        padding: 2rem 0;
    }

    .blog-hero-content h1 {
        font-size: 2rem;
    }

    .featured-article {
        padding: 2rem 0;
    }

    .featured-post {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .featured-content {
        padding: 1.5rem;
    }

    .blog-posts {
        padding: 2rem 0;
    }

    .blog-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .newsletter-signup {
        padding: 2rem 0;
    }

    .newsletter-form {
        flex-direction: column;
        align-items: center;
    }

    .newsletter-form input {
        width: 100%;
        max-width: 300px;
        min-width: auto;
    }

    /* About Page Responsive */
    .about-hero {
        padding: 2rem 0;
    }

    .about-hero-content h1 {
        font-size: 2rem;
    }

    .mission-content,
    .tech-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .mission-values {
        gap: 1rem;
    }

    .team-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .team-member {
        padding: 1.5rem;
    }

    .tech-features {
        grid-template-columns: 1fr;
    }

    .contact-cta {
        padding: 2rem 0;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    /* Contact Page Responsive */
    .contact-hero {
        padding: 2rem 0;
    }

    .contact-hero-content h1 {
        font-size: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-method {
        padding: 1rem;
    }

    .faq-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .faq-item {
        padding: 1.5rem;
    }

    .response-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .response-item {
        padding: 1.5rem;
    }

    /* Legal Pages Responsive */
    .legal-hero {
        padding: 2rem 0;
    }

    .legal-hero-content h1 {
        font-size: 2rem;
    }

    .legal-content {
        padding: 2rem 0;
    }

    .legal-document {
        padding: 0 1rem;
    }

    .legal-document h2 {
        font-size: 1.5rem;
        margin: 2rem 0 1rem;
    }

    .legal-document h3 {
        font-size: 1.2rem;
    }

    .legal-footer {
        padding: 1.5rem;
        margin-top: 2rem;
    }

    /* Quiz Grid */
    .recent-quizzes {
        padding: 2rem 0;
    }

    .quiz-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Footer */
    .footer {
        padding: 2rem 0 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }

    /* Modal */
    .modal-content {
        margin: 10% auto;
        width: 95%;
        max-height: 80vh;
    }

    .quiz-container {
        padding: 1rem;
    }

    .quiz-actions {
        flex-direction: column;
        gap: 1rem;
    }

    /* Buttons */
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

/* Small mobile screens (480px and below) */
@media (max-width: 480px) {
    .container {
        padding: 0 0.5rem;
    }

    .nav-container {
        padding: 0 0.5rem;
    }

    .hero {
        padding: 1.5rem 0;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .hero-description {
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 1.5rem;
        margin-bottom: 2rem;
    }

    .categories,
    .recent-quizzes,
    .featured-quizzes,
    .stats-section {
        padding: 1.5rem 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    /* About Page Small Mobile */
    .about-hero-content h1 {
        font-size: 1.8rem;
    }

    .member-image img {
        width: 120px;
        height: 120px;
    }

    .value-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    /* Contact Page Small Mobile */
    .contact-hero-content h1 {
        font-size: 1.8rem;
    }

    .contact-method {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .contact-method i {
        margin-top: 0;
    }

    /* Legal Pages Small Mobile */
    .legal-hero-content h1 {
        font-size: 1.8rem;
    }

    .legal-document h2 {
        font-size: 1.3rem;
    }

    .legal-document h3 {
        font-size: 1.1rem;
    }

    .category-card {
        padding: 1rem;
    }

    .category-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .quiz-card-content {
        padding: 1rem;
    }

    .footer {
        padding: 1.5rem 0 1rem;
    }

    .modal-content {
        margin: 5% auto;
        width: 98%;
    }

    .quiz-container {
        padding: 0.5rem;
    }

    .question-text {
        font-size: 1rem;
    }

    .answer-option {
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .score-display {
        font-size: 2rem;
    }

    .btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
}

/* Extra small screens (360px and below) */
@media (max-width: 360px) {
    .nav-logo h1 {
        font-size: 1.5rem;
    }

    .hero-title {
        font-size: 1.6rem;
    }

    .section-title {
        font-size: 1.3rem;
    }

    .category-card h3 {
        font-size: 1.1rem;
    }

    .quiz-card h3 {
        font-size: 1rem;
    }

    .modal-content {
        width: 100%;
        margin: 0;
        border-radius: 0;
        max-height: 100vh;
    }
}

/* Landscape orientation for mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        padding: 1rem 0;
    }

    .hero-container {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .categories,
    .recent-quizzes {
        padding: 1rem 0;
    }

    .modal-content {
        margin: 2% auto;
        max-height: 95vh;
    }
}

/* Print styles */
@media print {
    .header,
    .footer,
    .hamburger,
    .theme-toggle,
    .modal {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero,
    .categories,
    .recent-quizzes {
        padding: 1rem 0;
    }

    .category-card,
    .quiz-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    }

    .btn-outline {
        border-width: 3px;
    }

    .answer-option {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .category-card:hover,
    .quiz-card:hover {
        transform: none;
    }
}
