<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz - QuizMaster</title>
    <meta name="description" content="Take fun quizzes and test your knowledge with QuizMaster.">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧠</text></svg>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .quiz-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .quiz-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .quiz-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .quiz-progress {
            background: var(--bg-tertiary);
            border-radius: var(--border-radius);
            height: 8px;
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .quiz-progress-bar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .question-card {
            background: var(--bg-primary);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-lg);
            margin-bottom: 2rem;
        }
        
        .question-number {
            color: var(--text-muted);
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .question-text {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2rem;
            line-height: 1.4;
        }
        
        .answers-grid {
            display: grid;
            gap: 1rem;
        }
        
        .answer-option {
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
            text-align: left;
        }
        
        .answer-option:hover {
            border-color: var(--primary-color);
            background: rgba(255, 107, 157, 0.1);
        }
        
        .answer-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }
        
        .quiz-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
        }
        
        .btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }
        
        .btn-secondary:hover {
            background: var(--border-color);
        }
        
        .quiz-results {
            text-align: center;
            display: none;
        }
        
        .results-score {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .results-message {
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
        }
        
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--bg-tertiary);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            text-align: center;
            color: #e74c3c;
            padding: 2rem;
            background: #fdf2f2;
            border-radius: var(--border-radius);
            margin: 2rem 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="index.html" class="brand-link">
                        <i class="fas fa-brain brand-icon"></i>
                        <span class="brand-text">QuizMaster</span>
                    </a>
                </div>
                <div class="nav-menu">
                    <a href="index.html" class="nav-link">
                        <i class="fas fa-home"></i> Home
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="quiz-container">
            <!-- Loading State -->
            <div id="loading" class="loading-spinner">
                <div class="spinner"></div>
            </div>
            
            <!-- Error State -->
            <div id="error" class="error-message" style="display: none;">
                <h3>Oops! Something went wrong</h3>
                <p>Unable to load quiz questions. Please try again later.</p>
                <a href="index.html" class="btn btn-primary">Back to Home</a>
            </div>
            
            <!-- Quiz Header -->
            <div id="quiz-header" class="quiz-header" style="display: none;">
                <h1 class="quiz-title">Knowledge Quiz</h1>
                <div class="quiz-progress">
                    <div id="progress-bar" class="quiz-progress-bar"></div>
                </div>
            </div>
            
            <!-- Question Card -->
            <div id="question-card" class="question-card" style="display: none;">
                <div id="question-number" class="question-number"></div>
                <div id="question-text" class="question-text"></div>
                <div id="answers-grid" class="answers-grid"></div>
            </div>
            
            <!-- Quiz Controls -->
            <div id="quiz-controls" class="quiz-controls" style="display: none;">
                <button id="prev-btn" class="btn btn-secondary" onclick="previousQuestion()">
                    <i class="fas fa-arrow-left"></i> Previous
                </button>
                <button id="next-btn" class="btn btn-primary" onclick="nextQuestion()">
                    Next <i class="fas fa-arrow-right"></i>
                </button>
            </div>
            
            <!-- Quiz Results -->
            <div id="quiz-results" class="quiz-results">
                <div id="results-score" class="results-score"></div>
                <div id="results-message" class="results-message"></div>
                <div class="quiz-controls">
                    <a href="index.html" class="btn btn-secondary">
                        <i class="fas fa-home"></i> Back to Home
                    </a>
                    <button class="btn btn-primary" onclick="restartQuiz()">
                        <i class="fas fa-redo"></i> Try Again
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="assets/js/quiz.js"></script>
</body>
</html>
