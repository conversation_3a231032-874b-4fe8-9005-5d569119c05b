// Quiz Application for Independent Quiz Page

// Quiz categories configuration
const QUIZ_CATEGORIES = {
    9: { name: 'General Knowledge', icon: 'fas fa-brain' },
    10: { name: 'Entertainment: Books', icon: 'fas fa-book' },
    11: { name: 'Entertainment: Film', icon: 'fas fa-film' },
    12: { name: 'Entertainment: Music', icon: 'fas fa-music' },
    13: { name: 'Entertainment: Musicals & Theatres', icon: 'fas fa-theater-masks' },
    14: { name: 'Entertainment: Television', icon: 'fas fa-tv' },
    15: { name: 'Entertainment: Video Games', icon: 'fas fa-gamepad' },
    16: { name: 'Entertainment: Board Games', icon: 'fas fa-chess' },
    17: { name: 'Science & Nature', icon: 'fas fa-flask' },
    18: { name: 'Science: Computers', icon: 'fas fa-laptop' },
    19: { name: 'Science: Mathematics', icon: 'fas fa-calculator' },
    20: { name: 'Mythology', icon: 'fas fa-dragon' },
    21: { name: 'Sports', icon: 'fas fa-football-ball' },
    22: { name: 'Geography', icon: 'fas fa-globe' },
    23: { name: 'History', icon: 'fas fa-landmark' },
    24: { name: 'Politics', icon: 'fas fa-balance-scale' },
    25: { name: 'Art', icon: 'fas fa-palette' },
    26: { name: 'Celebrities', icon: 'fas fa-star' },
    27: { name: 'Animals', icon: 'fas fa-paw' },
    28: { name: 'Vehicles', icon: 'fas fa-car' },
    29: { name: 'Entertainment: Comics', icon: 'fas fa-mask' },
    30: { name: 'Science: Gadgets', icon: 'fas fa-mobile-alt' },
    31: { name: 'Entertainment: Japanese Anime & Manga', icon: 'fas fa-torii-gate' },
    32: { name: 'Entertainment: Cartoon & Animations', icon: 'fas fa-smile' }
};

// Quiz Application Class
class QuizApp {
    constructor() {
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.score = 0;
        this.category = null;
        this.init();
    }

    init() {
        // Get category from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        this.category = urlParams.get('category');

        // Load quiz questions
        this.loadQuestions();
    }

// Start a quiz
function startQuiz(quizId, quizData, allQuestions = null) {
    currentQuiz = {
        id: quizId,
        data: quizData,
        questions: allQuestions || [quizData]
    };
    
    // If we have multiple questions, use them; otherwise fetch more
    if (allQuestions && allQuestions.length > 1) {
        quizQuestions = allQuestions;
    } else {
        // Fetch more questions for this category
        fetchMoreQuestions(quizData.category);
        quizQuestions = [quizData];
    }
    
    currentQuestionIndex = 0;
    score = 0;
    userAnswers = [];
    
    showQuizModal();
    displayQuestion();
}

// Fetch more questions for the quiz
async function fetchMoreQuestions(category) {
    try {
        // Find category ID by name
        let categoryId = 9; // Default to General Knowledge

        for (const [id, info] of Object.entries(QUIZ_CATEGORIES)) {
            if (info.name === category) {
                categoryId = parseInt(id);
                break;
            }
        }

        const additionalQuestions = await fetchQuizData(categoryId, 9); // Fetch 9 more to make 10 total

        if (additionalQuestions.length > 0) {
            quizQuestions = [...quizQuestions, ...additionalQuestions];
        }
    } catch (error) {
        console.error('Error fetching more questions:', error);
    }
}

// Show quiz modal
function showQuizModal() {
    const modal = document.getElementById('quiz-modal');
    const quizContainer = document.getElementById('quiz-container');
    
    if (modal && quizContainer) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

// Hide quiz modal
function hideQuizModal() {
    const modal = document.getElementById('quiz-modal');
    
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // Restore scrolling
    }
    
    // Reset quiz state
    currentQuiz = null;
    currentQuestionIndex = 0;
    score = 0;
    userAnswers = [];
    quizQuestions = [];
}

// Display current question
function displayQuestion() {
    const quizContainer = document.getElementById('quiz-container');
    if (!quizContainer || !quizQuestions[currentQuestionIndex]) return;
    
    const question = quizQuestions[currentQuestionIndex];
    const progress = ((currentQuestionIndex + 1) / quizQuestions.length) * 100;
    const categoryInfo = getCategoryInfo(question.category);

    // Prepare answers array
    const answers = [...question.incorrect_answers, question.correct_answer];
    shuffleArray(answers);

    quizContainer.innerHTML = `
        <div class="quiz-container">
            <div class="quiz-header">
                <div class="quiz-category">
                    <i class="${categoryInfo.icon}"></i>
                    <h2>${decodeHtmlEntities(question.category)}</h2>
                </div>
                <div class="quiz-progress">
                    <div class="quiz-progress-bar" style="width: ${progress}%"></div>
                </div>
                <p>Question ${currentQuestionIndex + 1} of ${quizQuestions.length}</p>
            </div>
            
            <div class="question-container">
                <h3 class="question-text">${decodeHtmlEntities(question.question)}</h3>
                
                <div class="answers-container">
                    ${answers.map((answer, index) => `
                        <div class="answer-option" onclick="selectAnswer('${encodeURIComponent(answer)}', '${encodeURIComponent(question.correct_answer)}')">
                            ${decodeHtmlEntities(answer)}
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <div class="quiz-actions">
                <button class="btn btn-secondary" onclick="hideQuizModal()">
                    <i class="fas fa-times"></i> Exit Quiz
                </button>
                <div class="quiz-info">
                    <span class="difficulty-badge difficulty-${question.difficulty}">
                        ${formatDifficulty(question.difficulty)}
                    </span>
                </div>
            </div>
        </div>
    `;
}

// Select an answer
function selectAnswer(selectedAnswer, correctAnswer) {
    const answerOptions = document.querySelectorAll('.answer-option');
    const decodedSelected = decodeURIComponent(selectedAnswer);
    const decodedCorrect = decodeURIComponent(correctAnswer);
    
    // Disable all options
    answerOptions.forEach(option => {
        option.style.pointerEvents = 'none';
        
        const optionText = option.textContent.trim();
        
        if (optionText === decodedCorrect) {
            option.classList.add('correct');
        } else if (optionText === decodedSelected && decodedSelected !== decodedCorrect) {
            option.classList.add('incorrect');
        }
    });
    
    // Record the answer
    const isCorrect = decodedSelected === decodedCorrect;
    userAnswers.push({
        question: quizQuestions[currentQuestionIndex].question,
        selectedAnswer: decodedSelected,
        correctAnswer: decodedCorrect,
        isCorrect: isCorrect
    });
    
    if (isCorrect) {
        score++;
    }
    
    // Show next button after a delay
    setTimeout(() => {
        showNextButton();
    }, 1500);
}

// Show next button
function showNextButton() {
    const quizActions = document.querySelector('.quiz-actions');
    if (!quizActions) return;
    
    const isLastQuestion = currentQuestionIndex >= quizQuestions.length - 1;
    
    quizActions.innerHTML = `
        <button class="btn btn-secondary" onclick="hideQuizModal()">
            <i class="fas fa-times"></i> Exit Quiz
        </button>
        <button class="btn btn-primary" onclick="${isLastQuestion ? 'showResults()' : 'nextQuestion()'}">
            <i class="fas fa-${isLastQuestion ? 'flag-checkered' : 'arrow-right'}"></i> 
            ${isLastQuestion ? 'Show Results' : 'Next Question'}
        </button>
    `;
}

// Go to next question
function nextQuestion() {
    currentQuestionIndex++;
    
    if (currentQuestionIndex < quizQuestions.length) {
        displayQuestion();
    } else {
        showResults();
    }
}

// Show quiz results
function showResults() {
    const quizContainer = document.getElementById('quiz-container');
    if (!quizContainer) return;
    
    const percentage = Math.round((score / quizQuestions.length) * 100);
    const resultMessage = getResultMessage(percentage);
    const resultImage = generateResultImage(percentage);
    
    quizContainer.innerHTML = `
        <div class="quiz-results">
            <h2>Quiz Complete!</h2>
            <div class="score-display">${score}/${quizQuestions.length}</div>
            <div class="score-percentage">${percentage}%</div>
            <p class="score-message">${resultMessage}</p>
            
            <img src="${resultImage}" alt="Result illustration" style="max-width: 300px; border-radius: var(--border-radius); margin: 1rem 0;" loading="lazy">
            
            <div class="result-actions">
                <button class="btn btn-primary" onclick="restartQuiz()">
                    <i class="fas fa-redo"></i> Try Again
                </button>
                <button class="btn btn-secondary" onclick="startRandomQuiz(); hideQuizModal();">
                    <i class="fas fa-random"></i> Random Quiz
                </button>
                <button class="btn btn-outline" onclick="hideQuizModal()">
                    <i class="fas fa-home"></i> Back to Home
                </button>
            </div>
            
            <div class="detailed-results">
                <h3>Review Your Answers</h3>
                <div class="answers-review">
                    ${userAnswers.map((answer, index) => `
                        <div class="answer-review ${answer.isCorrect ? 'correct' : 'incorrect'}">
                            <h4>Question ${index + 1}</h4>
                            <p class="review-question">${decodeHtmlEntities(answer.question)}</p>
                            <p class="review-answer">
                                <strong>Your answer:</strong> ${answer.selectedAnswer}
                                ${answer.isCorrect ? '<i class="fas fa-check text-green"></i>' : '<i class="fas fa-times text-red"></i>'}
                            </p>
                            ${!answer.isCorrect ? `<p class="review-correct"><strong>Correct answer:</strong> ${answer.correctAnswer}</p>` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

// Get result message based on percentage
function getResultMessage(percentage) {
    if (percentage >= 90) {
        return "Outstanding! You're a true quiz master! 🏆";
    } else if (percentage >= 80) {
        return "Excellent work! You really know your stuff! 🌟";
    } else if (percentage >= 70) {
        return "Great job! You did really well! 👏";
    } else if (percentage >= 60) {
        return "Good effort! Keep learning and improving! 📚";
    } else if (percentage >= 50) {
        return "Not bad! There's room for improvement! 💪";
    } else {
        return "Keep trying! Every quiz makes you smarter! 🧠";
    }
}

// Generate result image using Pollinations AI
function generateResultImage(percentage) {
    let prompt;
    
    if (percentage >= 90) {
        prompt = "golden trophy with confetti and celebration";
    } else if (percentage >= 80) {
        prompt = "silver medal with stars and achievement";
    } else if (percentage >= 70) {
        prompt = "bronze medal with thumbs up and success";
    } else if (percentage >= 60) {
        prompt = "books and learning with positive energy";
    } else {
        prompt = "encouraging study scene with motivation";
    }
    
    return `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}?width=300&height=200`;
}

// Restart the current quiz
function restartQuiz() {
    currentQuestionIndex = 0;
    score = 0;
    userAnswers = [];
    displayQuestion();
}

// Utility function to shuffle array
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Get category information
function getCategoryInfo(categoryName) {
    for (const [id, info] of Object.entries(QUIZ_CATEGORIES)) {
        if (info.name === categoryName) {
            return { id: parseInt(id), ...info };
        }
    }
    return { id: 9, name: 'General Knowledge', icon: 'fas fa-brain' };
}

// Get category by ID
function getCategoryById(categoryId) {
    return QUIZ_CATEGORIES[categoryId] || { name: 'General Knowledge', icon: 'fas fa-brain' };
}

// Utility function to decode HTML entities (reused from main.js)
function decodeHtmlEntities(text) {
    const textArea = document.createElement('textarea');
    textArea.innerHTML = text;
    return textArea.value;
}

// Utility function to format difficulty (reused from main.js)
function formatDifficulty(difficulty) {
    return difficulty.charAt(0).toUpperCase() + difficulty.slice(1);
}

// Enhanced quiz statistics
function getQuizStats() {
    if (!currentQuiz || userAnswers.length === 0) return null;

    const totalQuestions = quizQuestions.length;
    const answeredQuestions = userAnswers.length;
    const correctAnswers = userAnswers.filter(answer => answer.isCorrect).length;
    const accuracy = Math.round((correctAnswers / answeredQuestions) * 100);

    return {
        totalQuestions,
        answeredQuestions,
        correctAnswers,
        accuracy,
        remainingQuestions: totalQuestions - answeredQuestions
    };
}

// Add keyboard navigation for quiz
document.addEventListener('keydown', function(e) {
    if (currentQuiz && document.getElementById('quiz-modal').style.display === 'block') {
        const answerOptions = document.querySelectorAll('.answer-option');
        
        // Number keys 1-4 for selecting answers
        if (e.key >= '1' && e.key <= '4') {
            const index = parseInt(e.key) - 1;
            if (answerOptions[index] && answerOptions[index].style.pointerEvents !== 'none') {
                answerOptions[index].click();
            }
        }
        
        // Enter key for next question
        if (e.key === 'Enter') {
            const nextButton = document.querySelector('.quiz-actions .btn-primary');
            if (nextButton) {
                nextButton.click();
            }
        }
    }
});

// Add CSS for result styling
const resultStyles = `
    <style>
        .score-percentage {
            font-size: 2rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .result-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin: 2rem 0;
        }
        
        .detailed-results {
            margin-top: 2rem;
            text-align: left;
        }
        
        .answers-review {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem;
        }
        
        .answer-review {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: var(--border-radius);
            border-left: 4px solid;
        }
        
        .answer-review.correct {
            border-left-color: var(--accent-color);
            background-color: rgba(16, 185, 129, 0.1);
        }
        
        .answer-review.incorrect {
            border-left-color: #ef4444;
            background-color: rgba(239, 68, 68, 0.1);
        }
        
        .review-question {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .review-answer, .review-correct {
            margin-bottom: 0.25rem;
        }
        
        .text-green { color: var(--accent-color); }
        .text-red { color: #ef4444; }
        
        .difficulty-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .difficulty-easy {
            background-color: var(--accent-color);
            color: white;
        }
        
        .difficulty-medium {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .difficulty-hard {
            background-color: #ef4444;
            color: white;
        }
        
        @media (max-width: 768px) {
            .result-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .result-actions .btn {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
`;

// Inject styles into head
if (!document.querySelector('#quiz-result-styles')) {
    const styleElement = document.createElement('div');
    styleElement.id = 'quiz-result-styles';
    styleElement.innerHTML = resultStyles;
    document.head.appendChild(styleElement);
}
