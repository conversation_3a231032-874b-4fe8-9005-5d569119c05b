<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Integration Test - QuizMaster</title>
    <meta name="description" content="Test page for QuizMaster API integrations including OpenTDB and Pollinations AI.">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧠</text></svg>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <!-- Logo -->
                <div class="nav-logo">
                    <a href="index.html">
                        <h1>QuizMaster</h1>
                    </a>
                </div>
                
                <!-- Navigation Menu -->
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="blog/index.html" class="nav-link">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a href="about.html" class="nav-link">About</a>
                    </li>
                    <li class="nav-item">
                        <a href="contact.html" class="nav-link">Contact</a>
                    </li>
                </ul>
                
                <!-- Theme Toggle -->
                <div class="theme-toggle">
                    <input type="checkbox" id="theme-toggle" class="theme-checkbox">
                    <label for="theme-toggle" class="theme-label">
                        <i class="fas fa-sun"></i>
                        <i class="fas fa-moon"></i>
                    </label>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Test Hero Section -->
        <section class="test-hero">
            <div class="container">
                <div class="test-hero-content">
                    <h1>API Integration Test</h1>
                    <p>Comprehensive testing of QuizMaster's API integrations</p>
                </div>
            </div>
        </section>

        <!-- OpenTDB API Test -->
        <section class="api-test-section">
            <div class="container">
                <h2 class="section-title">OpenTDB API Test</h2>
                <div class="test-controls">
                    <div class="control-group">
                        <label for="category-select">Category:</label>
                        <select id="category-select">
                            <option value="9">General Knowledge</option>
                            <option value="11">Movies</option>
                            <option value="12">Music</option>
                            <option value="17">Science</option>
                            <option value="21">Sports</option>
                            <option value="23">History</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label for="difficulty-select">Difficulty:</label>
                        <select id="difficulty-select">
                            <option value="">Any</option>
                            <option value="easy">Easy</option>
                            <option value="medium">Medium</option>
                            <option value="hard">Hard</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label for="amount-input">Questions:</label>
                        <input type="number" id="amount-input" min="1" max="50" value="5">
                    </div>
                    
                    <button class="btn btn-primary" onclick="testOpenTDB()">
                        <i class="fas fa-play"></i> Test OpenTDB API
                    </button>
                </div>
                
                <div id="opentdb-results" class="test-results">
                    <p>Click the button above to test the OpenTDB API integration.</p>
                </div>
            </div>
        </section>

        <!-- Pollinations AI Test -->
        <section class="api-test-section">
            <div class="container">
                <h2 class="section-title">Pollinations AI Test</h2>
                <div class="test-controls">
                    <div class="control-group">
                        <label for="prompt-input">Image Prompt:</label>
                        <input type="text" id="prompt-input" placeholder="Enter image description..." value="colorful brain with gears and lightbulbs">
                    </div>
                    
                    <div class="control-group">
                        <label for="width-input">Width:</label>
                        <input type="number" id="width-input" min="100" max="1024" value="400">
                    </div>
                    
                    <div class="control-group">
                        <label for="height-input">Height:</label>
                        <input type="number" id="height-input" min="100" max="1024" value="300">
                    </div>
                    
                    <button class="btn btn-primary" onclick="testPollinations()">
                        <i class="fas fa-image"></i> Generate Image
                    </button>
                </div>
                
                <div id="pollinations-results" class="test-results">
                    <p>Click the button above to test the Pollinations AI image generation.</p>
                </div>
            </div>
        </section>

        <!-- Performance Test -->
        <section class="api-test-section">
            <div class="container">
                <h2 class="section-title">Performance Test</h2>
                <div class="test-controls">
                    <button class="btn btn-secondary" onclick="runPerformanceTest()">
                        <i class="fas fa-stopwatch"></i> Run Performance Test
                    </button>
                    <button class="btn btn-outline" onclick="clearAllResults()">
                        <i class="fas fa-trash"></i> Clear Results
                    </button>
                </div>
                
                <div id="performance-results" class="test-results">
                    <p>Click the button above to run a comprehensive performance test.</p>
                </div>
            </div>
        </section>

        <!-- Integration Status -->
        <section class="status-section">
            <div class="container">
                <h2 class="section-title">Integration Status</h2>
                <div class="status-grid">
                    <div class="status-item" id="opentdb-status">
                        <i class="fas fa-question-circle"></i>
                        <h3>OpenTDB API</h3>
                        <p class="status-text">Not tested</p>
                    </div>
                    
                    <div class="status-item" id="pollinations-status">
                        <i class="fas fa-image"></i>
                        <h3>Pollinations AI</h3>
                        <p class="status-text">Not tested</p>
                    </div>
                    
                    <div class="status-item" id="performance-status">
                        <i class="fas fa-tachometer-alt"></i>
                        <h3>Performance</h3>
                        <p class="status-text">Not tested</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>QuizMaster</h3>
                    <p>Your ultimate destination for fun and educational quizzes. Test your knowledge and discover new facts every day!</p>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="blog/index.html">Blog</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 QuizMaster. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/quiz.js"></script>
    <script src="assets/js/theme.js"></script>
    
    <script>
        // API Testing Functions
        
        async function testOpenTDB() {
            const category = document.getElementById('category-select').value;
            const difficulty = document.getElementById('difficulty-select').value;
            const amount = document.getElementById('amount-input').value;
            const resultsDiv = document.getElementById('opentdb-results');
            const statusItem = document.getElementById('opentdb-status');
            
            resultsDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing OpenTDB API...';
            
            try {
                const startTime = performance.now();
                let url = `https://opentdb.com/api.php?amount=${amount}&category=${category}&type=multiple`;
                if (difficulty) url += `&difficulty=${difficulty}`;
                
                const response = await fetch(url);
                const endTime = performance.now();
                const data = await response.json();
                
                if (data.response_code === 0) {
                    resultsDiv.innerHTML = `
                        <div class="success-result">
                            <h3><i class="fas fa-check-circle"></i> OpenTDB API Test Successful</h3>
                            <p><strong>Response Time:</strong> ${(endTime - startTime).toFixed(2)}ms</p>
                            <p><strong>Questions Retrieved:</strong> ${data.results.length}</p>
                            <p><strong>Sample Question:</strong> ${decodeHtmlEntities(data.results[0].question)}</p>
                            <p><strong>Category:</strong> ${data.results[0].category}</p>
                            <p><strong>Difficulty:</strong> ${data.results[0].difficulty}</p>
                        </div>
                    `;
                    updateStatus('opentdb-status', 'success', 'Connected');
                } else {
                    throw new Error(`API Error Code: ${data.response_code}`);
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error-result">
                        <h3><i class="fas fa-exclamation-triangle"></i> OpenTDB API Test Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
                updateStatus('opentdb-status', 'error', 'Failed');
            }
        }
        
        async function testPollinations() {
            const prompt = document.getElementById('prompt-input').value;
            const width = document.getElementById('width-input').value;
            const height = document.getElementById('height-input').value;
            const resultsDiv = document.getElementById('pollinations-results');
            const statusItem = document.getElementById('pollinations-status');
            
            resultsDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating image with Pollinations AI...';
            
            try {
                const startTime = performance.now();
                const imageUrl = `https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}?width=${width}&height=${height}`;
                
                // Test image loading
                const img = new Image();
                img.onload = function() {
                    const endTime = performance.now();
                    resultsDiv.innerHTML = `
                        <div class="success-result">
                            <h3><i class="fas fa-check-circle"></i> Pollinations AI Test Successful</h3>
                            <p><strong>Generation Time:</strong> ${(endTime - startTime).toFixed(2)}ms</p>
                            <p><strong>Prompt:</strong> ${prompt}</p>
                            <p><strong>Dimensions:</strong> ${width}x${height}</p>
                            <div class="generated-image">
                                <img src="${imageUrl}" alt="Generated image" style="max-width: 100%; border-radius: 8px; margin-top: 1rem;">
                            </div>
                        </div>
                    `;
                    updateStatus('pollinations-status', 'success', 'Connected');
                };
                
                img.onerror = function() {
                    throw new Error('Failed to load generated image');
                };
                
                img.src = imageUrl;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error-result">
                        <h3><i class="fas fa-exclamation-triangle"></i> Pollinations AI Test Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
                updateStatus('pollinations-status', 'error', 'Failed');
            }
        }
        
        async function runPerformanceTest() {
            const resultsDiv = document.getElementById('performance-results');
            const statusItem = document.getElementById('performance-status');
            
            resultsDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running performance tests...';
            
            try {
                const tests = [];
                
                // Test multiple OpenTDB requests
                for (let i = 0; i < 3; i++) {
                    const startTime = performance.now();
                    const response = await fetch('https://opentdb.com/api.php?amount=1&category=9');
                    const endTime = performance.now();
                    tests.push({
                        test: `OpenTDB Request ${i + 1}`,
                        time: endTime - startTime,
                        success: response.ok
                    });
                }
                
                // Test image generation
                const imgStartTime = performance.now();
                const testImg = new Image();
                await new Promise((resolve, reject) => {
                    testImg.onload = resolve;
                    testImg.onerror = reject;
                    testImg.src = 'https://image.pollinations.ai/prompt/test?width=100&height=100';
                });
                const imgEndTime = performance.now();
                tests.push({
                    test: 'Image Generation',
                    time: imgEndTime - imgStartTime,
                    success: true
                });
                
                const avgTime = tests.reduce((sum, test) => sum + test.time, 0) / tests.length;
                const successRate = (tests.filter(test => test.success).length / tests.length) * 100;
                
                resultsDiv.innerHTML = `
                    <div class="success-result">
                        <h3><i class="fas fa-check-circle"></i> Performance Test Complete</h3>
                        <p><strong>Average Response Time:</strong> ${avgTime.toFixed(2)}ms</p>
                        <p><strong>Success Rate:</strong> ${successRate.toFixed(1)}%</p>
                        <div class="test-details">
                            <h4>Individual Test Results:</h4>
                            ${tests.map(test => `
                                <p>${test.test}: ${test.time.toFixed(2)}ms ${test.success ? '✅' : '❌'}</p>
                            `).join('')}
                        </div>
                    </div>
                `;
                updateStatus('performance-status', 'success', `${avgTime.toFixed(0)}ms avg`);
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error-result">
                        <h3><i class="fas fa-exclamation-triangle"></i> Performance Test Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
                updateStatus('performance-status', 'error', 'Failed');
            }
        }
        
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status-item ${status}`;
            element.querySelector('.status-text').textContent = text;
        }
        
        function clearAllResults() {
            document.getElementById('opentdb-results').innerHTML = '<p>Click the button above to test the OpenTDB API integration.</p>';
            document.getElementById('pollinations-results').innerHTML = '<p>Click the button above to test the Pollinations AI image generation.</p>';
            document.getElementById('performance-results').innerHTML = '<p>Click the button above to run a comprehensive performance test.</p>';
            
            updateStatus('opentdb-status', '', 'Not tested');
            updateStatus('pollinations-status', '', 'Not tested');
            updateStatus('performance-status', '', 'Not tested');
        }
        
        function decodeHtmlEntities(text) {
            const textArea = document.createElement('textarea');
            textArea.innerHTML = text;
            return textArea.value;
        }
        
        // Auto-run basic connectivity test on page load
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                const response = await fetch('https://opentdb.com/api.php?amount=1&category=9');
                if (response.ok) {
                    updateStatus('opentdb-status', 'success', 'Available');
                }
            } catch (error) {
                updateStatus('opentdb-status', 'error', 'Unavailable');
            }
        });
    </script>
</body>
</html>
